{"name": "dycast", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@types/vue-virtual-scroller": "npm:@earltp/vue-virtual-scroller@^1.0.1", "pako": "^2.1.0", "sass": "^1.87.0", "vue": "^3.5.13", "vue-virtual-scroller": "2.0.0-beta.8"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@types/pako": "^2.0.3", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}